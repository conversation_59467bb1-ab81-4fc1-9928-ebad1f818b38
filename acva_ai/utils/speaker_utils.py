"""
Utility functions for handling speaker diarization data.
"""

import asyncio
import logging
from typing import Dict, List, Optional

from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.llm_providers.llm_helpers import LLMProviderError
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def classify_speaker(
    llm_orchestrator: LLMOrchestrator,
    speaker_text: str,
    speaker_id: str = "unknown",
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Classify a speaker based on their speech content using LLM.

    Args:
        llm_orchestrator: The LLM orchestrator instance to use for classification
        speaker_text: The text spoken by the speaker to analyze
        speaker_id: Optional speaker identifier for context
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Classification result: "doctor", "patient", or "other"

    Raises:
        Exception: If LLM call fails or returns invalid classification
    """
    if not speaker_text or not speaker_text.strip():
        logger.warning("Empty speaker text provided for speaker %s", speaker_id)
        return "other"

    # Create a detailed prompt for speaker classification
    prompt = f"""
IMPORTANT: Acesta este un CONTEXT MEDICAL în Română. Analizezi transcrierea unei conversații medicale pentru clasificarea vorbitorilor.

Ești un expert în analiza conversațiilor medicale. Sarcina ta este să clasifici vorbitorii în conversațiile medicale pe baza tiparelor de vorbire, vocabularului și conținutului. TREBUIE să alegi una din cele trei categorii.

REGULI DE CLASIFICARE:
TREBUIE să clasifici vorbitorul în EXACT UNA din aceste trei categorii:

1. **doctor** - Profesioniști medicali (doctori, asistente, specialiști, personal medical)

2. **pacient** - Pacienți sau reprezentanții lor (inclusiv familia care vorbește pentru pacient)

3. **altul** - Oricine altcineva (personal administrativ, tehnicieni, vorbitori nelegați)

ANALIZĂ:
Textul Vorbitorului: ```
{speaker_text}
```

INSTRUCȚIUNI:
1. Analizează vocabularul, tonul și conținutul vorbirii
2. Determină rolul vorbitorului bazat pe regulile de clasificare de mai sus
3. TREBUIE să alegi una din cele trei categorii
4. Dacă nu este clar, folosește "altul" ca implicit

FORMAT RĂSPUNS:
Răspunde DOAR cu un cuvânt: "doctor", "pacient", sau "altul"
NU include nicio explicație, comentariu sau text suplimentar.
"""

    try:
        # Call the LLM with the classification prompt
        response = await llm_orchestrator.call_llm(
            prompt=prompt,
            use_cache=use_cache,
            response_usage=response_usage,
            temperature=0,
        )

        # Clean and validate the response
        classification = response.strip().lower()

        # Ensure the response is one of the valid classifications
        valid_classifications = {"doctor", "pacient", "altul"}
        if classification not in valid_classifications:
            logger.warning(
                "LLM returned invalid classification '%s' for speaker %s. "
                "Expected one of: %s. Defaulting to 'altul'.",
                classification,
                speaker_id,
                valid_classifications,
            )
            return "altu;"

        logger.info("Speaker %s classified as: %s", speaker_id, classification)
        return classification

    except LLMProviderError as e:
        logger.error(
            "LLM provider failed to classify speaker %s: %s", speaker_id, str(e)
        )
        # Return "other" as a safe default if classification fails
        return "altul"
    except Exception as e:
        logger.error("Unexpected error classifying speaker %s: %s", speaker_id, str(e))
        # Return "other" as a safe default if classification fails
        return "altul"


async def classify_speakers_from_visit_report(
    task_id: str,
    speakers_data: Optional[Union[Dict[str, List[str]], List[Dict[str, str]]]],
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> Optional[Dict[str, str]]:
    """
    Classify speakers from visit report speaker data in parallel.

    Args:
        task_id: Task identifier for logging
        speakers_data: Either a dictionary mapping speaker IDs to lists of their sentences
                      or a list of dictionaries with 'speaker' and 'sentence' keys
        llm_orchestrator: LLM orchestrator for classification
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Dictionary mapping speaker IDs to classifications or None if no speaker data
    """
    if not speakers_data:
        logger.info("No speaker data available for classification in task %s", task_id)
        return None

    logger.info("Starting speaker classification for task %s", task_id)

    # Convert new format to old format for processing
    if isinstance(speakers_data, list):
        # New format: list of {"speaker": "speaker_1", "sentence": "text"}
        converted_speakers_data = {}
        for item in speakers_data:
            speaker_id = item.get("speaker", "unknown")
            sentence = item.get("sentence", "")
            if speaker_id not in converted_speakers_data:
                converted_speakers_data[speaker_id] = []
            converted_speakers_data[speaker_id].append(sentence)
        speakers_data = converted_speakers_data

    # Create classification tasks for parallel execution
    classification_tasks = []
    speaker_ids = []

    for speaker_id, sentences in speakers_data.items():
        if not sentences:
            logger.warning(
                "No sentences found for speaker %s in task %s", speaker_id, task_id
            )
            continue

        # Combine all sentences for this speaker to get more context
        combined_text = " ".join(sentences)

        # Create classification task
        task = classify_speaker(
            llm_orchestrator=llm_orchestrator,
            speaker_text=combined_text,
            speaker_id=speaker_id,
            use_cache=True,
            response_usage=response_usage,
        )
        classification_tasks.append(task)
        speaker_ids.append(speaker_id)

    if not classification_tasks:
        logger.warning("No valid speakers found for classification in task %s", task_id)
        return {}

    # Run all classifications in parallel
    logger.info(
        "Classifying %d speakers in parallel for task %s",
        len(classification_tasks),
        task_id,
    )
    results = await asyncio.gather(*classification_tasks, return_exceptions=True)

    # Build the classification dictionary
    speaker_classifications = {}
    for i, speaker_id in enumerate(speaker_ids):
        result = results[i]
        if isinstance(result, Exception):
            logger.error(
                "Failed to classify speaker %s in task %s: %s",
                speaker_id,
                task_id,
                str(result),
            )
            speaker_classifications[speaker_id] = "other"  # Default fallback
        else:
            speaker_classifications[speaker_id] = result

    logger.info(
        "Speaker classification completed for task %s: %s",
        task_id,
        speaker_classifications,
    )
    return speaker_classifications
